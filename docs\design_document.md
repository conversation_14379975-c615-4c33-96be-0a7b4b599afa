# HKAutotest-Next 服务器测试框架设计文档

## 1. 项目概述

### 1.1 项目目标
HKAutotest-Next 是一个功能完善、强大的服务器测试框架，旨在为服务器硬件和软件提供全面的自动化测试解决方案。

### 1.2 核心特性
- **多连接方式支持**：SSH、Telnet、Local、Serial、RPC、PowerShell
- **多协议集成**：IPMI、Redfish、SNMP、RESTful、Web、Busctl
- **厂商自动识别**：智能识别不同厂商设备并适配相应协议
- **插件扩展机制**：支持第三方插件和Hook系统
- **设备生态扩展**：支持交换机、PDU等辅助设备
- **外部服务集成**：NFS、SFTP、SNMPTrap、邮件服务器等
- **全面测试覆盖**：BMC、BIOS、OS、硬件、压力、长稳测试

## 2. 系统架构设计

### 2.1 分层架构
```
┌─────────────────────────────────────────────────────────┐
│                   应用层 (Application Layer)              │
│  测试用例 | 测试报告 | Web界面 | CLI工具                    │
├─────────────────────────────────────────────────────────┤
│                   插件层 (Plugin Layer)                   │
│  插件管理器 | Hook系统 | 扩展接口                          │
├─────────────────────────────────────────────────────────┤
│                 测试引擎层 (Test Engine Layer)             │
│  调度器 | 执行器 | 结果收集器 | 并发控制                    │
├─────────────────────────────────────────────────────────┤
│                  服务层 (Service Layer)                   │
│  NFS | SFTP | SNMPTrap | 邮件服务 | 日志服务               │
├─────────────────────────────────────────────────────────┤
│                  设备层 (Device Layer)                    │
│  服务器 | 交换机 | PDU | 厂商识别 | 设备抽象               │
├─────────────────────────────────────────────────────────┤
│                  协议层 (Protocol Layer)                  │
│  IPMI | Redfish | SNMP | RESTful | Web | Busctl          │
├─────────────────────────────────────────────────────────┤
│                  连接层 (Connection Layer)                │
│  SSH | Telnet | Serial | Local | RPC | PowerShell        │
└─────────────────────────────────────────────────────────┘
```

### 2.2 技术栈选择
- **主语言**：Python 3.8+
- **异步框架**：asyncio + aiohttp
- **配置管理**：YAML + Pydantic
- **数据库**：SQLite (默认) + PostgreSQL (可选)
- **Web框架**：FastAPI
- **日志系统**：structlog + loguru
- **测试框架**：基于pytest扩展
- **报告生成**：Jinja2 + WeasyPrint (PDF)
- **容器化**：Docker + Docker Compose

## 3. 核心模块设计

### 3.1 连接管理器 (ConnectionManager)
```python
class ConnectionManager:
    """统一连接管理器"""
    
    async def create_connection(self, conn_type: str, config: dict) -> BaseConnection
    async def get_connection(self, device_id: str) -> BaseConnection
    async def close_connection(self, device_id: str) -> None
    async def health_check(self, device_id: str) -> bool
```

**支持的连接类型**：
- `SSHConnection`: SSH连接，支持密钥和密码认证
- `TelnetConnection`: Telnet连接，支持自动登录
- `SerialConnection`: 串口连接，支持多种波特率
- `LocalConnection`: 本地命令执行
- `RPCConnection`: RPC调用，支持gRPC和JSON-RPC
- `PowerShellConnection`: PowerShell远程连接

### 3.2 协议适配器 (ProtocolAdapter)
```python
class ProtocolAdapter:
    """协议适配器基类"""
    
    async def execute_command(self, command: str, **kwargs) -> ProtocolResponse
    async def get_device_info(self) -> DeviceInfo
    async def health_check(self) -> bool
```

**支持的协议**：
- `IPMIAdapter`: IPMI协议，支持ipmitool和python-ipmi
- `RedfishAdapter`: Redfish RESTful API
- `SNMPAdapter`: SNMP v1/v2c/v3协议
- `RESTfulAdapter`: 通用REST API适配器
- `WebAdapter`: Web界面自动化 (Selenium/Playwright)
- `BusctlAdapter`: systemd busctl接口

### 3.3 设备抽象层 (DeviceAbstraction)
```python
class BaseDevice:
    """设备基类"""
    
    def __init__(self, device_id: str, config: DeviceConfig):
        self.device_id = device_id
        self.config = config
        self.connections = {}
        self.protocols = {}
    
    async def initialize(self) -> None
    async def discover_capabilities(self) -> List[str]
    async def get_status(self) -> DeviceStatus
```

**设备类型**：
- `Server`: 服务器设备
- `Switch`: 网络交换机
- `PDU`: 电源分配单元
- `StorageDevice`: 存储设备
- `NetworkDevice`: 网络设备

### 3.4 厂商识别与适配系统

#### 3.4.1 厂商差异化问题分析
不同厂商的服务器在BMC、BIOS等方面存在显著差异：

**协议接口差异**：
- Dell iDRAC：支持iDRAC REST API (非标准Redfish) + 标准IPMI
- HP iLO：支持iLO REST API + XML-RPC + 标准IPMI
- Lenovo XCC：基于Redfish但有Lenovo扩展字段
- Supermicro：主要依赖标准IPMI，Web界面差异很大
- 华为iBMC：有自己的REST API规范，与Redfish差异较大
- 浪潮TSMA：有独特的管理接口和协议扩展

**返回数据格式差异**：
- 传感器信息：Dell返回详细的传感器状态，HP返回简化格式
- 系统信息：各厂商的JSON结构和字段名称不同
- 错误码：每个厂商有自己的错误码体系
- 单位和精度：温度、电压等数值的单位和精度不统一

**功能支持差异**：
- 虚拟媒体：实现方式和支持格式不同
- 电源管理：支持的电源状态和控制方式不同
- 固件更新：更新流程和文件格式差异很大
- 用户管理：权限模型和认证方式不同

#### 3.4.2 厂商适配架构设计

```python
# 1. 统一设备接口定义
class BMCInterface(ABC):
    """BMC统一接口"""

    @abstractmethod
    async def get_system_info(self) -> SystemInfo:
        """获取系统信息"""
        pass

    @abstractmethod
    async def get_sensors(self) -> List[Sensor]:
        """获取传感器信息"""
        pass

    @abstractmethod
    async def power_control(self, action: PowerAction) -> PowerResult:
        """电源控制"""
        pass

    @abstractmethod
    async def get_sel_logs(self, count: int = 100) -> List[SELEntry]:
        """获取SEL日志"""
        pass

    @abstractmethod
    async def get_firmware_info(self) -> FirmwareInfo:
        """获取固件信息"""
        pass

# 2. 厂商适配器工厂
class VendorAdapterFactory:
    """厂商适配器工厂"""

    _adapters: Dict[str, Type[BMCInterface]] = {}
    _detectors: List[VendorDetector] = []

    @classmethod
    def register_adapter(cls, vendor: str, adapter_class: Type[BMCInterface]):
        """注册厂商适配器"""
        cls._adapters[vendor] = adapter_class

    @classmethod
    def register_detector(cls, detector: VendorDetector):
        """注册厂商检测器"""
        cls._detectors.append(detector)

    @classmethod
    async def create_adapter(cls, device_config: DeviceConfig) -> BMCInterface:
        """创建厂商适配器"""
        vendor_info = await cls._detect_vendor(device_config)
        adapter_class = cls._adapters.get(vendor_info.vendor)
        if not adapter_class:
            raise UnsupportedVendorError(f"Unsupported vendor: {vendor_info.vendor}")
        return adapter_class(device_config, vendor_info)

    @classmethod
    async def _detect_vendor(cls, device_config: DeviceConfig) -> VendorInfo:
        """检测设备厂商 - 按优先级顺序检测"""
        # 检测器优先级：Web特征 > Redfish > IPMI
        detector_priority = [
            WebFeatureVendorDetector(),
            RedfishVendorDetector(),
            IPMIVendorDetector()
        ]

        for detector in detector_priority:
            try:
                vendor_info = await detector.detect(device_config)
                if vendor_info and vendor_info.confidence >= 0.5:
                    logger.info(f"Vendor detected: {vendor_info.vendor} "
                              f"(method: {vendor_info.detection_method}, "
                              f"confidence: {vendor_info.confidence:.2f})")
                    return vendor_info
            except Exception as e:
                logger.warning(f"Vendor detection failed with {detector.__class__.__name__}: {e}")

        raise VendorDetectionError("Unable to detect vendor with sufficient confidence")

# 3. 厂商信息数据模型
@dataclass
class VendorInfo:
    """厂商信息"""
    vendor: str                    # 厂商名称 (dell, hp, lenovo, etc.)
    detection_method: str          # 检测方法 (web_feature, ipmi, redfish)
    confidence: float              # 置信度 (0.0-1.0)
    model: Optional[str] = None    # 设备型号
    firmware_version: Optional[str] = None  # 固件版本
    details: Optional[dict] = None # 详细信息

# 4. 厂商检测器基类
class VendorDetector(ABC):
    """厂商检测器基类"""

    @abstractmethod
    async def detect(self, device_config: DeviceConfig) -> Optional[VendorInfo]:
        """检测厂商信息"""
        pass

# 4. Web特征厂商检测器（最准确的方法）
class WebFeatureVendorDetector(VendorDetector):
    """基于BMC Web界面特征的厂商检测器"""

    # 厂商特征数据库
    VENDOR_SIGNATURES = {
        "dell": {
            "files": [
                "/public/css/idrac.css",
                "/public/images/dell_logo.png",
                "/public/js/idrac.js",
                "/public/fonts/DellBrand.woff"
            ],
            "content_patterns": [
                ("/login.html", r"iDRAC"),
                ("/login.html", r"Dell.*Integrated.*Remote.*Access"),
                ("/public/css/idrac.css", r"\.idrac-"),
                ("/", r"Dell.*iDRAC")
            ],
            "headers": {
                "Server": r"Dell.*iDRAC"
            },
            "weight": 1.0
        },
        "hp": {
            "files": [
                "/css/ilo.css",
                "/images/hpe_logo.svg",
                "/images/hp_logo.png",
                "/js/ilo.js"
            ],
            "content_patterns": [
                ("/login.html", r"iLO"),
                ("/login.html", r"Hewlett.*Packard.*Enterprise"),
                ("/", r"HP.*iLO")
            ],
            "headers": {
                "Server": r"HP.*iLO"
            },
            "weight": 1.0
        },
        "lenovo": {
            "files": [
                "/public/css/xcc.css",
                "/public/images/lenovo_logo.png",
                "/public/js/xcc.js"
            ],
            "content_patterns": [
                ("/login.html", r"XClarity"),
                ("/login.html", r"Lenovo.*XClarity.*Controller"),
                ("/", r"Lenovo.*XCC")
            ],
            "weight": 1.0
        },
        "supermicro": {
            "files": [
                "/public/css/supermicro.css",
                "/public/images/supermicro_logo.gif",
                "/public/images/smc_logo.png"
            ],
            "content_patterns": [
                ("/login.html", r"Supermicro"),
                ("/login.html", r"IPMI.*Configuration"),
                ("/", r"Super.*Micro")
            ],
            "weight": 1.0
        },
        "huawei": {
            "files": [
                "/bmc/css/huawei.css",
                "/bmc/images/huawei_logo.png",
                "/public/css/ibmc.css"
            ],
            "content_patterns": [
                ("/login.html", r"iBMC"),
                ("/login.html", r"Huawei.*iBMC"),
                ("/", r"Huawei.*Intelligent.*Baseboard")
            ],
            "weight": 1.0
        },
        "inspur": {
            "files": [
                "/public/css/inspur.css",
                "/public/images/inspur_logo.png",
                "/public/css/tsma.css"
            ],
            "content_patterns": [
                ("/login.html", r"TSMA"),
                ("/login.html", r"Inspur.*TSMA"),
                ("/", r"浪潮|Inspur")
            ],
            "weight": 1.0
        }
    }

    async def detect(self, device_config: DeviceConfig) -> Optional[VendorInfo]:
        """通过Web特征检测厂商"""
        bmc_ip = self._get_bmc_ip(device_config)
        if not bmc_ip:
            return None

        logger.info(f"Starting web feature detection for {bmc_ip}")

        # 并发检测所有厂商特征
        detection_tasks = []
        for vendor, signatures in self.VENDOR_SIGNATURES.items():
            task = self._check_vendor_signatures(bmc_ip, vendor, signatures)
            detection_tasks.append(task)

        try:
            results = await asyncio.gather(*detection_tasks, return_exceptions=True)

            # 计算各厂商得分
            vendor_scores = {}
            for vendor, result in zip(self.VENDOR_SIGNATURES.keys(), results):
                if isinstance(result, Exception):
                    logger.debug(f"Detection failed for {vendor}: {result}")
                    vendor_scores[vendor] = 0.0
                else:
                    vendor_scores[vendor] = result

            # 选择得分最高的厂商
            if vendor_scores:
                best_vendor = max(vendor_scores, key=vendor_scores.get)
                best_score = vendor_scores[best_vendor]

                if best_score >= 0.5:  # 置信度阈值
                    logger.info(f"Detected vendor: {best_vendor} (confidence: {best_score:.2f})")
                    return VendorInfo(
                        vendor=best_vendor,
                        detection_method="web_feature",
                        confidence=best_score,
                        details={"scores": vendor_scores}
                    )
                else:
                    logger.warning(f"Low confidence detection: {vendor_scores}")

        except Exception as e:
            logger.error(f"Web feature detection failed: {e}")

        return None

    def _get_bmc_ip(self, device_config: DeviceConfig) -> Optional[str]:
        """获取BMC IP地址"""
        # 优先从web连接配置获取
        web_config = device_config.connections.get("web")
        if web_config and web_config.get("host"):
            return web_config["host"]

        # 从其他连接配置推断
        for conn_type in ["ipmi", "redfish", "ssh"]:
            conn_config = device_config.connections.get(conn_type)
            if conn_config and conn_config.get("host"):
                return conn_config["host"]

        return None

    async def _check_vendor_signatures(self, bmc_ip: str, vendor: str, signatures: dict) -> float:
        """检查特定厂商的特征签名"""
        score = 0.0
        total_checks = 0

        # 创建HTTP客户端，忽略SSL证书
        timeout = aiohttp.ClientTimeout(total=10, connect=5)
        connector = aiohttp.TCPConnector(ssl=False, limit=10)

        async with aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers={"User-Agent": "HKAutotest-BMC-Detector/1.0"}
        ) as session:

            # 检查特征文件存在性
            for file_path in signatures.get("files", []):
                total_checks += 1
                if await self._check_file_exists(session, bmc_ip, file_path):
                    score += 1.0
                    logger.debug(f"Found {vendor} signature file: {file_path}")

            # 检查内容模式匹配
            for file_path, pattern in signatures.get("content_patterns", []):
                total_checks += 1
                if await self._check_content_pattern(session, bmc_ip, file_path, pattern):
                    score += 1.0
                    logger.debug(f"Matched {vendor} content pattern in {file_path}")

            # 检查HTTP响应头
            for header, pattern in signatures.get("headers", {}).items():
                total_checks += 1
                if await self._check_header_pattern(session, bmc_ip, header, pattern):
                    score += 1.0
                    logger.debug(f"Matched {vendor} header pattern: {header}")

        final_score = (score / total_checks) if total_checks > 0 else 0.0
        logger.debug(f"Vendor {vendor} score: {final_score:.2f} ({score}/{total_checks})")
        return final_score

    async def _check_file_exists(self, session: aiohttp.ClientSession, bmc_ip: str, file_path: str) -> bool:
        """检查文件是否存在"""
        urls = [f"https://{bmc_ip}{file_path}", f"http://{bmc_ip}{file_path}"]

        for url in urls:
            try:
                async with session.head(url, allow_redirects=True) as response:
                    if response.status == 200:
                        return True
            except Exception:
                continue
        return False

    async def _check_content_pattern(self, session: aiohttp.ClientSession, bmc_ip: str, file_path: str, pattern: str) -> bool:
        """检查文件内容是否匹配模式"""
        urls = [f"https://{bmc_ip}{file_path}", f"http://{bmc_ip}{file_path}"]

        for url in urls:
            try:
                async with session.get(url, allow_redirects=True) as response:
                    if response.status == 200:
                        content = await response.text()
                        if re.search(pattern, content, re.IGNORECASE):
                            return True
            except Exception:
                continue
        return False

    async def _check_header_pattern(self, session: aiohttp.ClientSession, bmc_ip: str, header: str, pattern: str) -> bool:
        """检查HTTP响应头是否匹配模式"""
        urls = [f"https://{bmc_ip}/", f"http://{bmc_ip}/"]

        for url in urls:
            try:
                async with session.head(url, allow_redirects=True) as response:
                    header_value = response.headers.get(header, "")
                    if re.search(pattern, header_value, re.IGNORECASE):
                        return True
            except Exception:
                continue
        return False

# 5. IPMI厂商检测器（备用方法）
class IPMIVendorDetector(VendorDetector):
    """基于IPMI的厂商检测"""

    VENDOR_IDS = {
        0x0002A2: "dell",
        0x00000B: "hp",
        0x0005A3: "lenovo",
        0x002A7C: "supermicro",
        0x00007D: "huawei",
        0x0028EF: "inspur"
    }

    async def detect(self, device_config: DeviceConfig) -> Optional[VendorInfo]:
        try:
            ipmi_conn = await self._create_ipmi_connection(device_config)
            device_id = await ipmi_conn.get_device_id()
            vendor_id = device_id.manufacturer_id

            vendor_name = self.VENDOR_IDS.get(vendor_id)
            if vendor_name:
                return VendorInfo(
                    vendor=vendor_name,
                    model=device_id.product_name,
                    firmware_version=device_id.firmware_revision,
                    detection_method="ipmi",
                    confidence=0.8
                )
        except Exception as e:
            logger.debug(f"IPMI vendor detection failed: {e}")
        return None

# 5. Redfish厂商检测器
class RedfishVendorDetector(VendorDetector):
    """基于Redfish的厂商检测"""

    async def detect(self, device_config: DeviceConfig) -> Optional[VendorInfo]:
        try:
            redfish_conn = await self._create_redfish_connection(device_config)
            system_info = await redfish_conn.get("/redfish/v1/Systems/1")

            manufacturer = system_info.get("Manufacturer", "").lower()
            model = system_info.get("Model", "")
            firmware_version = system_info.get("BiosVersion", "")

            # 厂商名称标准化
            vendor_mapping = {
                "dell inc.": "dell",
                "hewlett packard enterprise": "hp",
                "lenovo": "lenovo",
                "super micro computer": "supermicro",
                "huawei": "huawei",
                "inspur": "inspur"
            }

            vendor = vendor_mapping.get(manufacturer)
            if vendor:
                return VendorInfo(
                    vendor=vendor,
                    model=model,
                    firmware_version=firmware_version,
                    detection_method="redfish"
                )
        except Exception as e:
            logger.debug(f"Redfish vendor detection failed: {e}")
        return None
```

#### 3.4.3 具体厂商适配器实现

```python
# Dell适配器
@VendorAdapterFactory.register_adapter("dell")
class DellBMCAdapter(BMCInterface):
    """Dell iDRAC适配器"""

    def __init__(self, device_config: DeviceConfig, vendor_info: VendorInfo):
        self.device_config = device_config
        self.vendor_info = vendor_info
        self.idrac_client = None
        self.ipmi_client = None

    async def get_system_info(self) -> SystemInfo:
        """获取Dell系统信息"""
        try:
            # 优先使用iDRAC REST API
            data = await self.idrac_client.get("/redfish/v1/Systems/System.Embedded.1")
            return self._convert_dell_system_info(data)
        except Exception:
            # 降级到IPMI
            data = await self.ipmi_client.get_system_info()
            return self._convert_ipmi_system_info(data)

    async def get_sensors(self) -> List[Sensor]:
        """获取Dell传感器信息"""
        sensors = []

        # 获取温度传感器
        temp_data = await self.idrac_client.get("/redfish/v1/Chassis/System.Embedded.1/Thermal")
        sensors.extend(self._convert_dell_temperature_sensors(temp_data))

        # 获取电压传感器
        power_data = await self.idrac_client.get("/redfish/v1/Chassis/System.Embedded.1/Power")
        sensors.extend(self._convert_dell_power_sensors(power_data))

        return sensors

    def _convert_dell_system_info(self, data: dict) -> SystemInfo:
        """转换Dell系统信息格式"""
        return SystemInfo(
            manufacturer="Dell",
            model=data.get("Model", ""),
            serial_number=data.get("SerialNumber", ""),
            bios_version=data.get("BiosVersion", ""),
            power_state=self._convert_dell_power_state(data.get("PowerState")),
            boot_source=data.get("Boot", {}).get("BootSourceOverrideTarget", ""),
            memory_total=self._calculate_total_memory(data.get("MemorySummary", {})),
            cpu_count=data.get("ProcessorSummary", {}).get("Count", 0)
        )

    def _convert_dell_temperature_sensors(self, data: dict) -> List[Sensor]:
        """转换Dell温度传感器数据"""
        sensors = []
        temperatures = data.get("Temperatures", [])

        for temp in temperatures:
            sensor = Sensor(
                name=temp.get("Name", ""),
                sensor_type=SensorType.TEMPERATURE,
                value=temp.get("ReadingCelsius"),
                unit="°C",
                status=self._convert_dell_sensor_status(temp.get("Status", {})),
                thresholds=self._convert_dell_thresholds(temp)
            )
            sensors.append(sensor)

        return sensors

# HP适配器
@VendorAdapterFactory.register_adapter("hp")
class HPBMCAdapter(BMCInterface):
    """HP iLO适配器"""

    async def get_system_info(self) -> SystemInfo:
        """获取HP系统信息"""
        # HP iLO有自己的API结构
        data = await self.ilo_client.get("/rest/v1/Systems/1")
        return self._convert_hp_system_info(data)

    async def get_sensors(self) -> List[Sensor]:
        """获取HP传感器信息"""
        # HP的传感器数据结构与Dell不同
        thermal_data = await self.ilo_client.get("/rest/v1/Chassis/1/Thermal")
        return self._convert_hp_sensors(thermal_data)

    def _convert_hp_system_info(self, data: dict) -> SystemInfo:
        """转换HP系统信息格式"""
        # HP的数据结构转换逻辑
        return SystemInfo(
            manufacturer="HP",
            model=data.get("ProductName", ""),
            serial_number=data.get("SerialNumber", ""),
            # HP特有的字段映射
            bios_version=data.get("Bios", {}).get("Current", {}).get("VersionString", ""),
            power_state=self._convert_hp_power_state(data.get("Power")),
            # ... 其他字段转换
        )
```

## 4. 测试引擎设计

### 4.1 测试引擎架构
```python
class TestEngine:
    """测试引擎核心"""
    
    def __init__(self):
        self.scheduler = TestScheduler()
        self.executor = TestExecutor()
        self.collector = ResultCollector()
        self.reporter = TestReporter()
    
    async def run_test_suite(self, suite: TestSuite) -> TestResult
    async def run_single_test(self, test: TestCase) -> TestResult
```

### 4.2 测试用例基类
```python
class BaseTestCase:
    """测试用例基类"""
    
    def __init__(self, name: str, config: TestConfig):
        self.name = name
        self.config = config
        self.device = None
        self.logger = None
    
    async def setup(self) -> None
    async def execute(self) -> TestResult
    async def teardown(self) -> None
    async def cleanup(self) -> None
```

### 4.3 测试分类

#### 4.3.1 BMC功能测试
- IPMI命令测试
- Redfish API测试
- Web界面功能测试
- 传感器监控测试
- 事件日志测试
- 用户管理测试

#### 4.3.2 BIOS功能测试
- BIOS设置验证
- 启动顺序测试
- UEFI Shell测试
- 安全启动测试
- 固件更新测试

#### 4.3.3 OS功能测试
- 系统信息收集
- 服务状态检查
- 网络配置验证
- 文件系统测试
- 性能基准测试

#### 4.3.4 硬件测试
- **CPU测试**: stress-ng, sysbench, CoreMark
- **内存测试**: memtest86+, stress, stream
- **网卡测试**: iperf3, netperf, 网络延迟测试
- **硬盘测试**: fio, dd, smartctl
- **PCIe测试**: lspci, 带宽测试, 错误检测

#### 4.3.5 压力测试
- CPU满载测试
- 内存压力测试
- IO密集测试
- 网络压力测试
- 综合压力测试

#### 4.3.6 长稳测试
- AC/DC循环测试
- 重启循环测试
- 温度循环测试
- 黑白名单检测
- 卡件降速检测
- SEL日志监控
- 告警检测

## 5. 插件系统设计

### 5.1 插件架构
```python
class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        self.plugins = {}
        self.hooks = HookManager()
    
    def register_plugin(self, plugin: BasePlugin) -> None
    def unregister_plugin(self, plugin_name: str) -> None
    def get_plugin(self, plugin_name: str) -> BasePlugin
    async def call_hook(self, hook_name: str, *args, **kwargs) -> Any
```

### 5.2 Hook系统
支持的Hook点：
- `before_test_suite`: 测试套件开始前
- `after_test_suite`: 测试套件结束后
- `before_test_case`: 测试用例开始前
- `after_test_case`: 测试用例结束后
- `on_test_error`: 测试出错时
- `on_device_connect`: 设备连接时
- `on_device_disconnect`: 设备断开时

### 5.3 插件接口
```python
class BasePlugin:
    """插件基类"""
    
    def __init__(self, name: str, version: str):
        self.name = name
        self.version = version
    
    async def initialize(self) -> None
    async def finalize(self) -> None
    def get_hooks(self) -> Dict[str, Callable]
```

## 6. 配置管理

### 6.1 配置层次结构
```yaml
# 全局配置 (config/global.yaml)
global:
  log_level: INFO
  max_concurrent_tests: 10
  timeout: 300
  
# 设备配置 (config/devices.yaml)
devices:
  server01:
    type: server
    vendor: dell
    connections:
      ssh:
        host: *************
        username: root
        password: password
      ipmi:
        host: *************
        username: admin
        password: admin
        
# 测试配置 (config/tests.yaml)
test_suites:
  bmc_basic:
    tests:
      - ipmi_info_test
      - redfish_system_test
    timeout: 600
```

### 6.2 配置验证
使用Pydantic进行配置验证和类型检查：
```python
class DeviceConfig(BaseModel):
    type: str
    vendor: Optional[str]
    connections: Dict[str, ConnectionConfig]
    protocols: Dict[str, ProtocolConfig]
```

## 7. 日志和监控

### 7.1 日志系统
- **结构化日志**: 使用structlog进行结构化日志记录
- **日志级别**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **日志输出**: 控制台、文件、远程日志服务器
- **日志轮转**: 按大小和时间轮转

### 7.2 监控指标
- 测试执行时间
- 设备连接状态
- 资源使用情况
- 错误率统计
- 性能基准数据

## 8. 报告系统

### 8.1 报告格式
- **HTML报告**: 交互式Web报告
- **PDF报告**: 正式文档报告
- **JSON报告**: 机器可读格式
- **JUnit XML**: CI/CD集成

### 8.2 报告内容
- 测试执行摘要
- 详细测试结果
- 错误日志和堆栈跟踪
- 性能图表和趋势
- 设备信息和配置

## 9. 部署和运维

### 9.1 部署方式
- **单机部署**: 所有组件在一台机器
- **分布式部署**: 测试节点分离
- **容器化部署**: Docker容器
- **云原生部署**: Kubernetes

### 9.2 运维特性
- 健康检查接口
- 性能监控
- 自动故障恢复
- 配置热更新
- 日志聚合

## 10. 开发计划

### 10.1 开发阶段
1. **阶段1**: 核心框架和基础设施 (4周)
2. **阶段2**: 连接和协议层 (6周)
3. **阶段3**: 设备抽象和厂商识别 (4周)
4. **阶段4**: 测试引擎和基础用例 (8周)
5. **阶段5**: 插件系统和扩展 (4周)
6. **阶段6**: 报告和监控系统 (4周)
7. **阶段7**: 优化和文档完善 (2周)

### 10.2 里程碑
- **M1**: 基础框架完成
- **M2**: 核心功能实现
- **M3**: 插件系统完成
- **M4**: 生产就绪版本

## 11. 风险评估

### 11.1 技术风险
- 多协议兼容性问题
- 厂商设备差异性
- 并发测试稳定性
- 性能瓶颈

### 11.2 缓解措施
- 充分的兼容性测试
- 模块化设计降低耦合
- 完善的错误处理机制
- 性能监控和优化

## 12. 项目目录结构

基于您的要求，将框架和测试用例明确分离，并规范测试用例结构：

```
hkautotest-next/
├── README.md                    # 项目说明文档
├── pyproject.toml              # 项目配置和依赖
├── docker-compose.yml          # Docker编排文件
├── Dockerfile                  # Docker镜像构建文件
├── .env.example               # 环境变量示例
├── .gitignore                 # Git忽略文件
├── pytest.ini                 # pytest配置文件
├──
├── lib/                        # 框架核心库
│   ├── __init__.py
│   ├──
│   ├── hkautotest/            # 主框架包
│   │   ├── __init__.py
│   │   ├── main.py            # 框架入口点
│   │   ├── cli.py             # 命令行接口
│   │   ├──
│   │   ├── core/              # 核心框架
│   │   │   ├── __init__.py
│   │   │   ├── config.py      # 配置管理
│   │   │   ├── exceptions.py  # 自定义异常
│   │   │   ├── logging.py     # 日志配置
│   │   │   ├── registry.py    # 组件注册器
│   │   │   └── utils.py       # 核心工具函数
│   │   │
│   │   ├── connections/       # 连接层
│   │   │   ├── __init__.py
│   │   │   ├── base.py        # 连接基类
│   │   │   ├── manager.py     # 连接管理器
│   │   │   ├── ssh.py         # SSH连接
│   │   │   ├── telnet.py      # Telnet连接
│   │   │   ├── serial.py      # 串口连接
│   │   │   ├── local.py       # 本地连接
│   │   │   ├── rpc.py         # RPC连接
│   │   │   └── powershell.py  # PowerShell连接
│   │   │
│   │   ├── protocols/         # 协议层
│   │   │   ├── __init__.py
│   │   │   ├── base.py        # 协议基类
│   │   │   ├── ipmi/          # IPMI协议
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base.py    # IPMI基类
│   │   │   │   ├── standard.py # 标准IPMI实现
│   │   │   │   └── vendors/   # 厂商特定IPMI实现
│   │   │   │       ├── __init__.py
│   │   │   │       ├── dell.py    # Dell IPMI扩展
│   │   │   │       ├── hp.py      # HP IPMI扩展
│   │   │   │       ├── lenovo.py  # Lenovo IPMI扩展
│   │   │   │       ├── supermicro.py # Supermicro IPMI扩展
│   │   │   │       ├── huawei.py  # 华为IPMI扩展
│   │   │   │       └── inspur.py  # 浪潮IPMI扩展
│   │   │   ├── redfish/       # Redfish协议
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base.py    # Redfish基类
│   │   │   │   ├── standard.py # 标准Redfish实现
│   │   │   │   └── vendors/   # 厂商特定Redfish实现
│   │   │   │       ├── __init__.py
│   │   │   │       ├── dell.py    # Dell iDRAC API
│   │   │   │       ├── hp.py      # HP iLO API
│   │   │   │       ├── lenovo.py  # Lenovo XCC API
│   │   │   │       ├── supermicro.py # Supermicro API
│   │   │   │       ├── huawei.py  # 华为iBMC API
│   │   │   │       └── inspur.py  # 浪潮TSMA API
│   │   │   ├── web/           # Web自动化
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base.py    # Web基类
│   │   │   │   └── vendors/   # 厂商特定Web实现
│   │   │   │       ├── __init__.py
│   │   │   │       ├── dell.py    # Dell iDRAC Web
│   │   │   │       ├── hp.py      # HP iLO Web
│   │   │   │       ├── lenovo.py  # Lenovo XCC Web
│   │   │   │       ├── supermicro.py # Supermicro Web
│   │   │   │       ├── huawei.py  # 华为iBMC Web
│   │   │   │       └── inspur.py  # 浪潮TSMA Web
│   │   │   ├── snmp.py        # SNMP协议
│   │   │   ├── restful.py     # RESTful协议
│   │   │   └── busctl.py      # Busctl协议
│   │   │
│   │   ├── devices/           # 设备层
│   │   │   ├── __init__.py
│   │   │   ├── base.py        # 设备基类
│   │   │   ├── server.py      # 服务器设备
│   │   │   ├── switch.py      # 交换机设备
│   │   │   ├── pdu.py         # PDU设备
│   │   │   ├── storage.py     # 存储设备
│   │   │   └── network.py     # 网络设备
│   │   │
│   │   ├── services/          # 外部服务层
│   │   │   ├── __init__.py
│   │   │   ├── base.py        # 服务基类
│   │   │   ├── nfs.py         # NFS服务
│   │   │   ├── sftp.py        # SFTP服务
│   │   │   ├── snmp_trap.py   # SNMP Trap服务
│   │   │   ├── email.py       # 邮件服务
│   │   │   ├── database.py    # 数据库服务
│   │   │   └── monitoring.py  # 监控服务
│   │   │
│   │   ├── engine/            # 测试引擎
│   │   │   ├── __init__.py
│   │   │   ├── scheduler.py   # 测试调度器
│   │   │   ├── executor.py    # 测试执行器
│   │   │   ├── collector.py   # 结果收集器
│   │   │   ├── runner.py      # 测试运行器
│   │   │   ├── context.py     # 测试上下文
│   │   │   └── base.py        # 测试基类
│   │   │
│   │   ├── plugins/           # 插件系统
│   │   │   ├── __init__.py
│   │   │   ├── manager.py     # 插件管理器
│   │   │   ├── base.py        # 插件基类
│   │   │   ├── hooks.py       # Hook系统
│   │   │   └── builtin/       # 内置插件
│   │   │       ├── __init__.py
│   │   │       ├── reporter.py # 报告插件
│   │   │       ├── monitor.py  # 监控插件
│   │   │       └── notifier.py # 通知插件
│   │   │
│   │   ├── tools/             # 测试工具封装
│   │   │   ├── __init__.py
│   │   │   ├── base.py        # 工具基类
│   │   │   ├── stress/        # 压测工具封装
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base.py    # 压测工具基类
│   │   │   │   ├── cpu/       # CPU压测工具
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── stress_ng.py    # stress-ng封装
│   │   │   │   │   ├── sysbench.py     # sysbench CPU测试封装
│   │   │   │   │   ├── coremark.py     # CoreMark封装
│   │   │   │   │   └── prime95.py      # Prime95封装
│   │   │   │   ├── memory/    # 内存压测工具
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── memtest.py      # memtest封装
│   │   │   │   │   ├── stress_ng_mem.py # stress-ng内存测试
│   │   │   │   │   ├── stream.py       # STREAM基准测试
│   │   │   │   │   └── mbw.py          # Memory Bandwidth测试
│   │   │   │   ├── storage/   # 存储压测工具
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── fio.py          # FIO封装
│   │   │   │   │   ├── dd.py           # DD命令封装
│   │   │   │   │   ├── iozone.py       # IOzone封装
│   │   │   │   │   └── hdparm.py       # hdparm封装
│   │   │   │   ├── network/   # 网络压测工具
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── iperf3.py       # iperf3封装
│   │   │   │   │   ├── netperf.py      # netperf封装
│   │   │   │   │   ├── ping.py         # ping测试封装
│   │   │   │   │   └── iperf.py        # iperf封装
│   │   │   │   └── comprehensive/      # 综合压测工具
│   │   │   │       ├── __init__.py
│   │   │   │       ├── stress_ng_all.py # stress-ng综合测试
│   │   │   │       ├── unixbench.py    # UnixBench封装
│   │   │   │       └── geekbench.py    # GeekBench封装
│   │   │   ├── monitoring/    # 监控工具封装
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base.py    # 监控工具基类
│   │   │   │   ├── system/    # 系统监控
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── top.py          # top命令封装
│   │   │   │   │   ├── htop.py         # htop封装
│   │   │   │   │   ├── iostat.py       # iostat封装
│   │   │   │   │   ├── vmstat.py       # vmstat封装
│   │   │   │   │   └── sar.py          # sar封装
│   │   │   │   ├── hardware/  # 硬件监控
│   │   │   │   │   ├── __init__.py
│   │   │   │   │   ├── sensors.py      # lm-sensors封装
│   │   │   │   │   ├── smartctl.py     # smartctl封装
│   │   │   │   │   └── dmidecode.py    # dmidecode封装
│   │   │   │   └── network/   # 网络监控
│   │   │   │       ├── __init__.py
│   │   │   │       ├── netstat.py      # netstat封装
│   │   │   │       ├── ss.py           # ss命令封装
│   │   │   │       └── tcpdump.py      # tcpdump封装
│   │   │   └── analysis/      # 分析工具封装
│   │   │       ├── __init__.py
│   │   │       ├── base.py    # 分析工具基类
│   │   │       ├── log_analyzer.py     # 日志分析器
│   │   │       ├── performance_analyzer.py # 性能分析器
│   │   │       └── report_generator.py # 报告生成器
│   │   │
│   │   ├── utils/             # 工具模块
│   │   │   ├── __init__.py
│   │   │   ├── decorators.py  # 装饰器
│   │   │   ├── validators.py  # 验证器
│   │   │   ├── parsers.py     # 解析器
│   │   │   ├── formatters.py  # 格式化器
│   │   │   ├── crypto.py      # 加密工具
│   │   │   └── network.py     # 网络工具
│   │   │
│   │   ├── reports/           # 报告模块
│   │   │   ├── __init__.py
│   │   │   ├── base.py        # 报告基类
│   │   │   ├── html.py        # HTML报告
│   │   │   ├── pdf.py         # PDF报告
│   │   │   ├── json.py        # JSON报告
│   │   │   ├── junit.py       # JUnit XML报告
│   │   │   └── templates/     # 报告模板
│   │   │       ├── html/
│   │   │       └── pdf/
│   │   │
│   │   └── web/               # Web界面 (可选)
│   │       ├── __init__.py
│   │       ├── app.py         # FastAPI应用
│   │       ├── api/           # API路由
│   │       ├── static/        # 静态文件
│   │       └── templates/     # 页面模板
│   │
│   └── unit_tests/            # 框架单元测试
│       ├── __init__.py
│       ├── conftest.py        # pytest配置
│       ├── test_connections/  # 连接层测试
│       ├── test_protocols/    # 协议层测试
│       ├── test_devices/      # 设备层测试
│       ├── test_engine/       # 引擎测试
│       └── test_plugins/      # 插件测试
│
├── tests/                     # 测试用例和AW相关
│   ├── __init__.py
│   ├── conftest.py           # 测试配置
│   ├──
│   ├── test_suites/          # 测试套件定义
│   │   ├── __init__.py
│   │   ├── bmc_basic.yaml    # BMC基础测试套件
│   │   ├── bios_basic.yaml   # BIOS基础测试套件
│   │   ├── hardware_full.yaml # 硬件全面测试套件
│   │   ├── stress_test.yaml  # 压力测试套件
│   │   └── longevity.yaml    # 长稳测试套件
│   │
│   ├── test_cases/           # 测试用例实现（参考老框架结构）
│   │   ├── __init__.py
│   │   ├──
│   │   ├── bmc/              # BMC测试用例
│   │   │   ├── __init__.py
│   │   │   ├── 01_chassis_board_management/    # 机箱板卡管理
│   │   │   │   ├── 00_cpu_management/
│   │   │   │   │   ├── 00_cpu_info/
│   │   │   │   │   │   ├── FRAME_CPU_INFO_FUNC_1020.py
│   │   │   │   │   │   └── FRAME_CPU_INFO_FUNC_1030.py
│   │   │   │   │   └── 01_cpu_control/
│   │   │   │   ├── 01_mem_management/
│   │   │   │   ├── 02_mainboard_management/
│   │   │   │   └── 03_riser_management/
│   │   │   ├── 02_storage_management/          # 存储管理
│   │   │   ├── 03_interface_management/        # 接口管理
│   │   │   ├── 04_cooling_management/          # 散热管理
│   │   │   ├── 05_power_management/            # 电源管理
│   │   │   ├── 06_config_management/           # 配置管理
│   │   │   ├── 07_security_management/         # 安全管理
│   │   │   ├── 08_maintenance_management/      # 维护管理
│   │   │   └── 09_service_management/          # 服务管理
│   │   │       ├── 01_interface/
│   │   │       │   ├── 01_ipmi_command/
│   │   │       │   │   ├── APP/
│   │   │       │   │   │   ├── SVC_INTF_IPMI_APP_FUNC_0010.py
│   │   │       │   │   │   ├── SVC_INTF_IPMI_APP_FUNC_0020.py
│   │   │       │   │   │   └── SVC_INTF_IPMI_APP_RELI_0010.py
│   │   │       │   │   ├── CHASSIS/
│   │   │       │   │   ├── SENSOR/
│   │   │       │   │   ├── STORAGE/
│   │   │       │   │   └── TRANSPORT/
│   │   │       │   ├── 02_redfish_command/
│   │   │       │   └── 03_web_interface/
│   │   │       └── 02_protocol/
│   │   │
│   │   ├── bios/             # BIOS测试用例
│   │   │   ├── __init__.py
│   │   │   ├── 01_boot_management/             # 启动管理
│   │   │   │   ├── 00_boot_sequence/
│   │   │   │   ├── 01_boot_options/
│   │   │   │   └── 02_secure_boot/
│   │   │   ├── 02_hardware_config/             # 硬件配置
│   │   │   │   ├── 00_cpu_config/
│   │   │   │   ├── 01_memory_config/
│   │   │   │   └── 02_pcie_config/
│   │   │   └── 03_system_settings/             # 系统设置
│   │   │
│   │   ├── server/           # 服务器测试用例
│   │   │   ├── __init__.py
│   │   │   ├── 01_system_info/                 # 系统信息
│   │   │   ├── 02_service_status/              # 服务状态
│   │   │   ├── 03_network_config/              # 网络配置
│   │   │   ├── 04_storage_mount/               # 存储挂载
│   │   │   └── 05_performance/                 # 性能测试
│   │   │
│   │   ├── hardware/         # 硬件测试用例
│   │   │   ├── __init__.py
│   │   │   ├── 01_cpu_test/                    # CPU测试
│   │   │   │   ├── 00_cpu_info/
│   │   │   │   ├── 01_cpu_performance/
│   │   │   │   └── 02_cpu_temperature/
│   │   │   ├── 02_memory_test/                 # 内存测试
│   │   │   │   ├── 00_memory_info/
│   │   │   │   ├── 01_memory_stress/
│   │   │   │   └── 02_memory_ecc/
│   │   │   ├── 03_network_test/                # 网络测试
│   │   │   │   ├── 00_interface_test/
│   │   │   │   ├── 01_bandwidth_test/
│   │   │   │   └── 02_latency_test/
│   │   │   ├── 04_storage_test/                # 存储测试
│   │   │   │   ├── 00_storage_info/
│   │   │   │   ├── 01_storage_io/
│   │   │   │   └── 02_storage_smart/
│   │   │   └── 05_pcie_test/                   # PCIe测试
│   │   │       ├── 00_pcie_info/
│   │   │       └── 01_pcie_bandwidth/
│   │   │
│   │   ├── stress/           # 压力测试用例
│   │   │   ├── __init__.py
│   │   │   ├── 01_cpu_stress/                  # CPU压力测试
│   │   │   ├── 02_memory_stress/               # 内存压力测试
│   │   │   ├── 03_io_stress/                   # IO压力测试
│   │   │   ├── 04_network_stress/              # 网络压力测试
│   │   │   └── 05_comprehensive_stress/        # 综合压力测试
│   │   │
│   │   ├── longevity/        # 长稳测试用例
│   │   │   ├── __init__.py
│   │   │   ├── 01_power_cycle/                 # 电源循环测试
│   │   │   ├── 02_reboot_cycle/                # 重启循环测试
│   │   │   ├── 03_thermal_cycle/               # 温度循环测试
│   │   │   ├── 04_sel_monitoring/              # SEL日志监控测试
│   │   │   └── 05_component_degradation/       # 组件降速检测测试
│   │   │
│   │   └── compatibility/    # 兼容性测试用例
│   │       ├── __init__.py
│   │       ├── 01_vendor_compatibility/        # 厂商兼容性
│   │       └── 02_version_compatibility/       # 版本兼容性
│   │
│   ├── fixtures/             # 测试夹具和工具
│   │   ├── __init__.py
│   │   ├── device_fixtures.py # 设备夹具
│   │   ├── data_fixtures.py   # 数据夹具
│   │   └── mock_fixtures.py   # 模拟夹具
│   │
│   ├── utils/                # 测试工具
│   │   ├── __init__.py
│   │   ├── assertions.py     # 自定义断言
│   │   ├── helpers.py        # 测试辅助函数
│   │   └── data_generators.py # 测试数据生成器
│   │
│   └── aw/                   # AW (Automated Workflow) 相关
│       ├── __init__.py
│       ├── workflows/        # 自动化工作流
│       │   ├── __init__.py
│       │   ├── daily_check.py # 日常检查工作流
│       │   ├── weekly_full.py # 周度全面测试工作流
│       │   └── release_validation.py # 发布验证工作流
│       ├── schedules/        # 调度配置
│       │   ├── __init__.py
│       │   ├── cron_jobs.yaml # 定时任务配置
│       │   └── triggers.yaml  # 触发器配置
│       └── reports/          # AW专用报告
│           ├── __init__.py
│           ├── dashboard.py  # 仪表板报告
│           └── notifications.py # 通知报告
│
├── config/                   # 配置文件
│   ├── global.yaml          # 全局配置
│   ├── devices.yaml         # 设备配置
│   ├── test_suites.yaml     # 测试套件配置
│   ├── logging.yaml         # 日志配置
│   ├── plugins.yaml         # 插件配置
│   └── environments/        # 环境配置
│       ├── dev.yaml         # 开发环境
│       ├── test.yaml        # 测试环境
│       └── prod.yaml        # 生产环境
│
├── docs/                     # 文档目录
│   ├── design_document.md   # 设计文档
│   ├── api_reference.md     # API参考
│   ├── user_guide.md        # 用户指南
│   ├── developer_guide.md   # 开发指南
│   ├── test_case_guide.md   # 测试用例编写指南
│   └── examples/            # 示例代码
│
├── scripts/                  # 脚本目录
│   ├── install.sh           # 安装脚本
│   ├── setup_dev.sh         # 开发环境设置
│   ├── run_tests.sh         # 运行测试脚本
│   └── deploy.sh            # 部署脚本
│
├── examples/                 # 示例和演示
│   ├── basic_usage.py       # 基础使用示例
│   ├── custom_plugin.py     # 自定义插件示例
│   └── test_scenarios/      # 测试场景示例
│
└── data/                     # 数据目录
    ├── logs/                # 日志文件
    ├── reports/             # 测试报告
    ├── cache/               # 缓存文件
    └── temp/                # 临时文件
```

## 13. 接口设计和调用规范

### 13.1 框架接口设计原则

#### 13.1.1 接口分层设计
lib层提供统一接口，tests层调用接口：

```python
# lib层提供统一接口
from lib.hkautotest.protocols.ipmi import IPMIInterface
from lib.hkautotest.tools.stress.cpu import CPUStressInterface
from lib.hkautotest.tools.monitoring.system import SystemMonitorInterface

# tests层调用接口
class FRAME_CPU_STRESS_FUNC_1020(Case):
    def execute(self):
        # 获取CPU压测接口
        cpu_stress = self.get_tool_interface("cpu_stress")

        # 获取系统监控接口
        system_monitor = self.get_tool_interface("system_monitor")

        # 执行压测
        stress_result = cpu_stress.run_stress_test(
            duration=300,
            threads=4,
            load_type="prime"
        )

        # 监控系统状态
        monitor_data = system_monitor.collect_metrics(
            duration=300,
            metrics=["cpu_usage", "temperature", "load_average"]
        )
```

#### 13.1.2 工具接口统一规范
```python
# 所有工具接口继承自基类
class ToolInterface(ABC):
    """工具接口基类"""

    def __init__(self, device, config=None):
        self.device = device
        self.config = config or {}
        self.logger = get_logger(self.__class__.__name__)

    @abstractmethod
    def initialize(self) -> bool:
        """初始化工具"""
        pass

    @abstractmethod
    def cleanup(self) -> bool:
        """清理工具"""
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """检查工具是否可用"""
        pass

# CPU压测接口
class CPUStressInterface(ToolInterface):
    """CPU压测工具统一接口"""

    def run_stress_test(self, duration: int, threads: int = None,
                       load_type: str = "cpu") -> StressResult:
        """运行CPU压测"""
        pass

    def run_benchmark(self, benchmark_type: str = "coremark") -> BenchmarkResult:
        """运行CPU基准测试"""
        pass

    def get_supported_tests(self) -> List[str]:
        """获取支持的测试类型"""
        pass

# 系统监控接口
class SystemMonitorInterface(ToolInterface):
    """系统监控工具统一接口"""

    def start_monitoring(self, metrics: List[str], interval: int = 1) -> str:
        """开始监控，返回监控会话ID"""
        pass

    def stop_monitoring(self, session_id: str) -> MonitorResult:
        """停止监控，返回监控结果"""
        pass

    def collect_metrics(self, duration: int, metrics: List[str]) -> MonitorResult:
        """收集指定时间的监控数据"""
        pass

    def get_current_status(self) -> SystemStatus:
        """获取当前系统状态"""
        pass
```

### 13.2 接口工厂模式
```python
# 接口工厂
class InterfaceFactory:
    """接口工厂，根据设备和配置创建对应的接口实例"""

    _protocol_interfaces = {
        "ipmi": IPMIInterface,
        "redfish": RedfishInterface,
        "web": WebInterface
    }

    _tool_interfaces = {
        "cpu_stress": CPUStressInterface,
        "memory_stress": MemoryStressInterface,
        "storage_stress": StorageStressInterface,
        "system_monitor": SystemMonitorInterface,
        "hardware_monitor": HardwareMonitorInterface
    }

    @classmethod
    def create_protocol_interface(cls, protocol_type: str, device, config=None):
        """创建协议接口"""
        interface_class = cls._protocol_interfaces.get(protocol_type)
        if not interface_class:
            raise InterfaceError(f"Unsupported protocol: {protocol_type}")

        return interface_class(device, config)

    @classmethod
    def create_tool_interface(cls, tool_type: str, device, config=None):
        """创建工具接口"""
        interface_class = cls._tool_interfaces.get(tool_type)
        if not interface_class:
            raise InterfaceError(f"Unsupported tool: {tool_type}")

        return interface_class(device, config)

# 测试基类中的接口获取方法
class Case:
    """测试用例基类"""

    def get_protocol_interface(self, protocol_type: str, device_id: str = None):
        """获取协议接口"""
        device = self.get_device(device_id) if device_id else self.device
        return InterfaceFactory.create_protocol_interface(protocol_type, device)

    def get_tool_interface(self, tool_type: str, device_id: str = None):
        """获取工具接口"""
        device = self.get_device(device_id) if device_id else self.device
        return InterfaceFactory.create_tool_interface(tool_type, device)
```

## 14. 测试用例命名和结构规范

### 13.1 测试用例命名规范（基于老框架格式）

#### 13.1.1 文件命名规范
```
<模块>_<功能>_<类型>_<编号>.py

命名组成：
- 模块：FRAME(框架), SVC(服务), HW(硬件), STRESS(压力), LONG(长稳)
- 功能：具体功能描述，如CPU_INFO, POWER_CTRL, SENSOR_READ
- 类型：FUNC(功能), RELI(可靠性), PERF(性能), STRESS(压力), BOUND(边界)
- 编号：4位数字，如1020, 1030

示例：
- FRAME_CPU_INFO_FUNC_1020.py        # 框架CPU信息功能测试
- SVC_INTF_IPMI_APP_FUNC_0010.py     # 服务接口IPMI应用功能测试
- HW_MEMORY_STRESS_RELI_2010.py      # 硬件内存压力可靠性测试
- STRESS_CPU_LOAD_PERF_3010.py       # 压力CPU负载性能测试
```

#### 13.1.2 测试类命名规范
```python
class <文件名>(Case):
    """测试类描述"""

示例：
class FRAME_CPU_INFO_FUNC_1020(Case):
    """CPU信息获取功能测试"""

class SVC_INTF_IPMI_APP_FUNC_0010(Case):
    """IPMI应用接口功能测试"""

class HW_MEMORY_STRESS_RELI_2010(Case):
    """内存压力可靠性测试"""
```

#### 13.1.3 测试用例ID规范
```
<模块>_<子模块>_<功能>_<类型>_<编号>

模块编码：
- FRAME: 框架基础功能
- SVC: 服务管理功能
- HW: 硬件相关功能
- STRESS: 压力测试
- LONG: 长稳测试
- COMPAT: 兼容性测试

类型编码：
- FUNC: 功能测试
- RELI: 可靠性测试
- PERF: 性能测试
- STRESS: 压力测试
- BOUND: 边界测试
- NEG: 负面测试

编号规则：
- 1000-1999: 基础功能测试
- 2000-2999: 可靠性测试
- 3000-3999: 性能测试
- 4000-4999: 压力测试
- 5000-5999: 边界测试
- 6000-6999: 负面测试
```

### 13.2 测试用例结构规范

#### 13.2.1 标准测试用例结构（基于老框架格式）
```python
"""
功   能：web查询CPU相关传感器信息测试

修改信息：
    日期：   2025/01/15
    修改内容：创建
    修改人：  开发者姓名/工号

版权信息：
©2025-2028 四川华鲲振宇智能科技有限责任公司
"""

from lib.hkautotest.engine.core.case import Case
from lib.hkautotest.core.config.enum import DeviceType, HostType
from tests.test_logic.alias import BMCMgt

class FRAME_CPU_INFO_FUNC_1020(Case):
    """
    CaseId:
        FRAME_CPU_INFO_FUNC_1020
    RunLevel:
        1(#1)
    CaseName:
        web查询CPU相关传感器信息测试
    PreCondition:
        1、环境正常
        2、BMC Web界面可正常访问
        3、OS系统正常运行
    TestStep:
        1._登录BMC_Web，进入系统管理->系统信息->传感器，查询CPU相关传感器信息，有预期1
        2._在OS下，执行ipmitool_sensor_list命令，查询CPU相关传感器信息，有预期2
        3._对比Web界面和IPMI命令获取的传感器信息，验证一致性，有预期3
    ExpectedResult:
        1._支持CPU温度相关传感器信息获取，对应传感器的当前温度值正常，状态为OK，无异常显示，每个CPU上报8个传感器信息到web网页
        2._查询成功，无异常显示，对应名称与web界面保持一致，温度值正常，状态为OK
        3._Web界面和IPMI命令获取的传感器信息保持一致，温度值误差在合理范围内
    """

    def create_meta_data(self):
        """
        添加测试Param
        """
        self.logger.info("create meta data Start....")

        # 设置测试用例基本信息
        self.case_id = "FRAME_CPU_INFO_FUNC_1020"
        self.case_name = "web查询CPU相关传感器信息测试"
        self.run_level = 1
        self.priority = 1
        self.tags = ["frame", "cpu", "sensor", "web", "functional"]

        # 设置设备信息
        self.device_type = DeviceType.Server
        self.host_type = HostType.BMC

        # 设置测试参数
        self.test_params = {
            "cpu_sensor_count": 8,  # 每个CPU预期的传感器数量
            "temp_threshold_low": 20,  # 温度下限
            "temp_threshold_high": 85,  # 温度上限
            "temp_tolerance": 5  # 温度误差容忍度
        }

        self.logger.info("create meta data End....")

    def setup(self):
        """
        测试前置条件
        """
        self.logger.info("Setup Start....")

        # 获取设备实例
        self.server = self.get_device("server01")
        self.bmc = self.server.get_host("bmc")
        self.os = self.server.get_host("host_os")

        # 检查BMC连接
        if not self.bmc.is_connected():
            self.bmc.connect()
            self.assert_true(self.bmc.is_connected(), "BMC连接失败")

        # 检查OS连接
        if not self.os.is_connected():
            self.os.connect()
            self.assert_true(self.os.is_connected(), "OS连接失败")

        # 检查Web界面可访问性
        web_status = self.bmc.check_web_accessibility()
        self.assert_true(web_status, "BMC Web界面不可访问")

        self.logger.info("Setup End....")

    def execute(self):
        """
        测试执行步骤
        """
        self.logger.info("Execute Start....")

        # Step 1: 通过Web界面获取CPU传感器信息
        self.logger.step("Step 1: 登录BMC Web，获取CPU传感器信息")
        web_sensors = self._get_cpu_sensors_from_web()
        self.assert_not_empty(web_sensors, "Web界面未获取到CPU传感器信息")

        # 验证Web传感器信息
        self._validate_web_sensors(web_sensors)

        # Step 2: 通过IPMI命令获取CPU传感器信息
        self.logger.step("Step 2: 通过IPMI命令获取CPU传感器信息")
        ipmi_sensors = self._get_cpu_sensors_from_ipmi()
        self.assert_not_empty(ipmi_sensors, "IPMI命令未获取到CPU传感器信息")

        # 验证IPMI传感器信息
        self._validate_ipmi_sensors(ipmi_sensors)

        # Step 3: 对比Web和IPMI获取的传感器信息
        self.logger.step("Step 3: 对比Web和IPMI传感器信息一致性")
        self._compare_sensors_consistency(web_sensors, ipmi_sensors)

        self.logger.info("Execute End....")

    def teardown(self):
        """
        测试清理工作
        """
        self.logger.info("Teardown Start....")

        # 清理Web会话
        if hasattr(self, 'bmc') and self.bmc.is_web_logged_in():
            self.bmc.web_logout()

        # 记录测试结果
        self.logger.info("测试用例执行完成")

        self.logger.info("Teardown End....")

    def _get_cpu_sensors_from_web(self):
        """通过Web界面获取CPU传感器信息"""
        # 登录Web界面
        login_result = self.bmc.web_login()
        self.assert_true(login_result, "Web登录失败")

        # 导航到传感器页面
        nav_result = self.bmc.web_navigate_to_sensors()
        self.assert_true(nav_result, "导航到传感器页面失败")

        # 获取CPU相关传感器
        cpu_sensors = self.bmc.web_get_cpu_sensors()
        return cpu_sensors

    def _get_cpu_sensors_from_ipmi(self):
        """通过IPMI命令获取CPU传感器信息"""
        # 执行IPMI传感器列表命令
        cmd_result = self.os.execute_command("ipmitool sensor list | grep -i cpu")
        self.assert_equal(cmd_result.return_code, 0, "IPMI传感器命令执行失败")

        # 解析IPMI输出
        cpu_sensors = self._parse_ipmi_sensor_output(cmd_result.stdout)
        return cpu_sensors

    def _validate_web_sensors(self, sensors):
        """验证Web传感器信息"""
        for sensor in sensors:
            # 验证传感器名称
            self.assert_not_empty(sensor.get("name"), "传感器名称不能为空")

            # 验证温度值
            temp_value = sensor.get("value")
            self.assert_not_none(temp_value, "传感器温度值不能为空")
            self.assert_range(temp_value,
                            self.test_params["temp_threshold_low"],
                            self.test_params["temp_threshold_high"],
                            "传感器温度值超出正常范围")

            # 验证状态
            status = sensor.get("status")
            self.assert_equal(status, "OK", f"传感器{sensor.get('name')}状态异常: {status}")

    def _validate_ipmi_sensors(self, sensors):
        """验证IPMI传感器信息"""
        # 类似的验证逻辑
        pass

    def _compare_sensors_consistency(self, web_sensors, ipmi_sensors):
        """对比传感器信息一致性"""
        # 实现传感器信息对比逻辑
        pass

    def _parse_ipmi_sensor_output(self, output):
        """解析IPMI传感器输出"""
        # 实现IPMI输出解析逻辑
        pass
```

#### 13.2.2 测试用例元数据规范
```python
# 测试用例装饰器
@test_case(
    name="测试用例名称",           # 必填：用例名称
    description="测试用例描述",     # 必填：用例描述
    priority=1,                   # 必填：优先级 1-5 (1最高)
    tags=["tag1", "tag2"],       # 必填：标签列表
    timeout=300,                 # 可选：超时时间(秒)
    retry=0,                     # 可选：重试次数
    depends_on=[],               # 可选：依赖的测试用例
    platforms=["linux", "windows"], # 可选：支持的平台
    vendors=["dell", "hp"],      # 可选：支持的厂商
    requirements=["REQ-001"]     # 可选：关联的需求ID
)

# 标签分类规范
TAGS = {
    # 测试类型
    "smoke": "冒烟测试",
    "functional": "功能测试",
    "integration": "集成测试",
    "performance": "性能测试",
    "stress": "压力测试",
    "longevity": "长稳测试",
    "boundary": "边界测试",
    "negative": "负面测试",

    # 测试模块
    "bmc": "BMC测试",
    "bios": "BIOS测试",
    "os": "操作系统测试",
    "hardware": "硬件测试",
    "network": "网络测试",
    "storage": "存储测试",

    # 测试级别
    "basic": "基础测试",
    "advanced": "高级测试",
    "expert": "专家测试"
}
```

#### 13.2.3 测试数据管理规范
```python
# 测试数据文件结构
tests/
├── data/                    # 测试数据目录
│   ├── bmc/
│   │   ├── device_info.json      # BMC设备信息测试数据
│   │   ├── sensor_data.json      # 传感器测试数据
│   │   └── user_accounts.json    # 用户账户测试数据
│   ├── hardware/
│   │   ├── cpu_specs.json        # CPU规格测试数据
│   │   └── memory_configs.json   # 内存配置测试数据
│   └── common/
│       ├── error_codes.json      # 错误码定义
│       └── test_constants.json   # 测试常量

# 测试数据加载示例
class TestBMCInfo(BaseTestCase):

    @classmethod
    def setup_class(cls):
        super().setup_class()
        # 加载测试数据
        cls.test_data = cls.load_test_data("bmc/device_info.json")
        cls.expected_fields = cls.test_data["required_fields"]
        cls.optional_fields = cls.test_data["optional_fields"]
```

#### 13.2.4 断言规范
```python
# 使用框架提供的断言方法
class BaseTestCase:
    """测试基类提供的断言方法"""

    def assert_equal(self, actual, expected, message=""):
        """断言相等"""

    def assert_not_equal(self, actual, expected, message=""):
        """断言不相等"""

    def assert_true(self, condition, message=""):
        """断言为真"""

    def assert_false(self, condition, message=""):
        """断言为假"""

    def assert_none(self, value, message=""):
        """断言为None"""

    def assert_not_none(self, value, message=""):
        """断言不为None"""

    def assert_in(self, item, container, message=""):
        """断言包含"""

    def assert_not_in(self, item, container, message=""):
        """断言不包含"""

    def assert_greater(self, a, b, message=""):
        """断言大于"""

    def assert_less(self, a, b, message=""):
        """断言小于"""

    def assert_regex_match(self, text, pattern, message=""):
        """断言正则匹配"""

    def assert_range(self, value, min_val, max_val, message=""):
        """断言在范围内"""
```

### 13.3 测试套件配置规范

#### 13.3.1 测试套件YAML格式
```yaml
# tests/suites/bmc_basic.yaml
name: "BMC基础功能测试套件"
description: "验证BMC的基本功能，包括信息获取、电源控制等"
version: "1.0.0"
author: "测试团队"
tags: ["bmc", "basic", "smoke"]

# 套件配置
config:
  timeout: 1800          # 套件超时时间(秒)
  parallel: false        # 是否并行执行
  retry_failed: 1        # 失败重试次数
  stop_on_failure: false # 失败时是否停止

# 前置条件
prerequisites:
  - name: "设备连接检查"
    type: "connectivity"
    target: "bmc"
  - name: "权限验证"
    type: "authentication"
    target: "bmc"

# 测试用例列表
test_cases:
  - module: "tests.cases.bmc.test_bmc_info"
    class: "TestBMCInfo"
    methods:
      - "test_get_bmc_device_id_success"
      - "test_get_bmc_mc_info_success"

  - module: "tests.cases.bmc.test_bmc_power_control"
    class: "TestBMCPowerControl"
    methods:
      - "test_power_status_query"
      - "test_power_on_server"
      - "test_power_off_server"

# 后置清理
cleanup:
  - name: "恢复电源状态"
    type: "power_restore"
  - name: "清理临时文件"
    type: "file_cleanup"
```

## 14. 核心依赖库

### 13.1 生产依赖
```toml
[tool.poetry.dependencies]
python = "^3.8"
asyncio = "*"
aiohttp = "^3.8.0"
fastapi = "^0.100.0"
uvicorn = "^0.23.0"
pydantic = "^2.0.0"
pyyaml = "^6.0"
structlog = "^23.0.0"
loguru = "^0.7.0"
sqlalchemy = "^2.0.0"
alembic = "^1.11.0"
jinja2 = "^3.1.0"
weasyprint = "^59.0"
paramiko = "^3.2.0"
pyserial = "^3.5"
pysnmp = "^4.4.12"
requests = "^2.31.0"
selenium = "^4.10.0"
playwright = "^1.36.0"
psutil = "^5.9.0"
click = "^8.1.0"
rich = "^13.4.0"
```

### 13.2 开发依赖
```toml
[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.11.0"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.4.0"
pre-commit = "^3.3.0"
```

## 14. API设计示例

### 14.1 设备管理API
```python
from fastapi import APIRouter, HTTPException
from typing import List, Optional

router = APIRouter(prefix="/api/v1/devices", tags=["devices"])

@router.get("/", response_model=List[DeviceInfo])
async def list_devices(
    device_type: Optional[str] = None,
    vendor: Optional[str] = None
) -> List[DeviceInfo]:
    """获取设备列表"""
    pass

@router.post("/", response_model=DeviceInfo)
async def create_device(device: DeviceCreate) -> DeviceInfo:
    """创建新设备"""
    pass

@router.get("/{device_id}", response_model=DeviceInfo)
async def get_device(device_id: str) -> DeviceInfo:
    """获取设备详情"""
    pass

@router.put("/{device_id}", response_model=DeviceInfo)
async def update_device(
    device_id: str,
    device: DeviceUpdate
) -> DeviceInfo:
    """更新设备信息"""
    pass

@router.delete("/{device_id}")
async def delete_device(device_id: str) -> dict:
    """删除设备"""
    pass

@router.post("/{device_id}/connect")
async def connect_device(device_id: str) -> dict:
    """连接设备"""
    pass

@router.post("/{device_id}/disconnect")
async def disconnect_device(device_id: str) -> dict:
    """断开设备连接"""
    pass

@router.get("/{device_id}/status")
async def get_device_status(device_id: str) -> DeviceStatus:
    """获取设备状态"""
    pass
```

### 14.2 测试执行API
```python
@router.post("/test-suites/{suite_id}/run")
async def run_test_suite(
    suite_id: str,
    devices: List[str],
    config: Optional[TestConfig] = None
) -> TestExecution:
    """执行测试套件"""
    pass

@router.get("/executions/{execution_id}")
async def get_execution_status(execution_id: str) -> TestExecution:
    """获取测试执行状态"""
    pass

@router.post("/executions/{execution_id}/stop")
async def stop_execution(execution_id: str) -> dict:
    """停止测试执行"""
    pass

@router.get("/executions/{execution_id}/results")
async def get_execution_results(execution_id: str) -> TestResults:
    """获取测试结果"""
    pass
```

---

*本设计文档将随着项目进展持续更新和完善。*
